package com.tqhit.battery.one.manager.charge

import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

@Singleton
class ChargingSessionManager @Inject constructor(private val preferencesHelper: PreferencesHelper) {
    companion object {
        private const val KEY_CHARGING_SESSIONS = "charging_sessions"
        private const val MAX_SESSIONS = 60 * 60 * 24 // Maximum number of sessions to keep
    }

    private val sessions = mutableListOf<ChargeSession>()

    init {
        loadSessions()
    }

    fun getAllSessions(): List<ChargeSession> {
        return sessions
    }

    private fun loadSessions() {
        val sessionsJson = preferencesHelper.getString(KEY_CHARGING_SESSIONS)
        if (!sessionsJson.isNullOrEmpty()) {
            sessions.clear()
            sessions.addAll(sessionsJson.split("|").mapNotNull { ChargeSession.fromString(it) })
        }
    }

    private fun saveSessions() {
        val sessionsJson = sessions.joinToString("|") { it.toString() }
        preferencesHelper.saveString(KEY_CHARGING_SESSIONS, sessionsJson)
    }

    fun addSession(session: ChargeSession) {
        sessions.add(session)
        if (sessions.size > MAX_SESSIONS) {
            sessions.removeAt(0)
        }
        saveSessions()
    }

    fun clearSessions() {
        sessions.clear()
        saveSessions()
    }

    fun getAverageScreenOnSpeed(): Double {
        val validSessions = sessions.filter { it.screenOnPercent > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.screenOnPercent }.average()
        } else 0.0
    }

    fun getAverageScreenOffSpeed(): Double {
        val validSessions = sessions.filter { it.screenOffPercent > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.screenOffPercent }.average()
        } else 0.0
    }

    fun getAverageSpeed(): Double {
        val validSessions = sessions.filter { it.averageSpeed > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.averageSpeed }.average()
        } else 0.0
    }

    fun getAverageScreenOnMilliAmperes(): Int {
        val validSessions = sessions.filter { it.screenOnMilliAmperes > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.screenOnMilliAmperes }.average().toInt()
        } else 0
    }

    fun getAverageScreenOffMilliAmperes(): Int {
        val validSessions = sessions.filter { it.screenOffMilliAmperes > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.screenOffMilliAmperes }.average().toInt()
        } else 0
    }

    fun getAverageMilliAmperes(): Int {
        val validSessions = sessions.filter { it.averageSpeedMilliAmperes > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.averageSpeedMilliAmperes }.average().toInt()
        } else 0
    }

    fun getTotalSessions(): Int = sessions.size

    /**
     * Adds sample charging sessions if none exist.
     * This is used for demonstration purposes as specified in the Health PRD.
     * Generates 10 sample sessions with realistic charging patterns.
     *
     * NOTE: Only works in DEBUG builds to prevent sample data in production.
     */
    fun addSampleSessionsIfEmpty() {
        // Only generate sample data in debug builds
        if (!com.tqhit.battery.one.BuildConfig.DEBUG) {
            android.util.Log.d("ChargingSessionManager", "Sample session generation disabled in release builds")
            return
        }

        android.util.Log.d("ChargingSessionManager", "Sample session generation enabled for DEBUG builds only")

        if (sessions.isNotEmpty()) {
            android.util.Log.d("ChargingSessionManager", "Sessions already exist (${sessions.size}), no sample generation needed")
            return // Already have sessions, no need to add samples
        }

        val currentTime = System.currentTimeMillis()
        val oneDayMillis = 24 * 60 * 60 * 1000L

        // Generate 10 sample sessions over the past 10 days
        for (i in 0 until 10) {
            val sessionStartTime = currentTime - (i * oneDayMillis) - (Random.nextDouble() * oneDayMillis / 2).toLong()
            val sessionDuration = (30 + Random.nextDouble() * 90).toLong() * 60 * 1000 // 30-120 minutes
            val sessionEndTime = sessionStartTime + sessionDuration

            val startPercent = (15 + Random.nextDouble() * 60).toInt() // 15-75%
            val endPercent = (startPercent + 20 + Random.nextDouble() * 25).toInt().coerceAtMost(100) // +20-45%

            val averageSpeed = (endPercent - startPercent).toDouble() / (sessionDuration / (60 * 60 * 1000.0)) // %/hour
            val averageSpeedMilliAmperes = (1000 + Random.nextDouble() * 2000).toInt() // 1000-3000 mA

            val sampleSession = ChargeSession(
                startTime = sessionStartTime,
                endTime = sessionEndTime,
                averageSpeed = averageSpeed,
                averageSpeedMilliAmperes = averageSpeedMilliAmperes,
                startPercent = startPercent,
                endPercent = endPercent,
                totalMilliAmperes = (averageSpeedMilliAmperes * (sessionDuration / (60 * 60 * 1000.0))).toInt(),
                screenOffPercent = Random.nextDouble() * 50, // 0-50% screen off time
                screenOffMilliAmperes = (averageSpeedMilliAmperes * 0.8).toInt(),
                screenOnPercent = Random.nextDouble() * 30, // 0-30% screen on time
                screenOnMilliAmperes = (averageSpeedMilliAmperes * 1.2).toInt()
            )

            sessions.add(sampleSession)
        }

        saveSessions()
        android.util.Log.d("ChargingSessionManager", "Generated ${sessions.size} sample charging sessions for health demonstration (DEBUG MODE ONLY)")
    }
}