package com.tqhit.battery.one.manager.charge

import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

@Singleton
class ChargingSessionManager @Inject constructor(private val preferencesHelper: PreferencesHelper) {
    companion object {
        private const val KEY_CHARGING_SESSIONS = "charging_sessions"
        private const val MAX_SESSIONS = 60 * 60 * 24 // Maximum number of sessions to keep
    }

    private val sessions = mutableListOf<ChargeSession>()

    init {
        loadSessions()
    }

    fun getAllSessions(): List<ChargeSession> {
        return sessions
    }

    private fun loadSessions() {
        val sessionsJson = preferencesHelper.getString(KEY_CHARGING_SESSIONS)
        if (!sessionsJson.isNullOrEmpty()) {
            sessions.clear()
            sessions.addAll(sessionsJson.split("|").mapNotNull { ChargeSession.fromString(it) })
        }
    }

    private fun saveSessions() {
        val sessionsJson = sessions.joinToString("|") { it.toString() }
        preferencesHelper.saveString(KEY_CHARGING_SESSIONS, sessionsJson)
    }

    fun addSession(session: ChargeSession) {
        sessions.add(session)
        if (sessions.size > MAX_SESSIONS) {
            sessions.removeAt(0)
        }
        saveSessions()
    }

    fun clearSessions() {
        sessions.clear()
        saveSessions()
    }

    fun getAverageScreenOnSpeed(): Double {
        val validSessions = sessions.filter { it.screenOnPercent > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.screenOnPercent }.average()
        } else 0.0
    }

    fun getAverageScreenOffSpeed(): Double {
        val validSessions = sessions.filter { it.screenOffPercent > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.screenOffPercent }.average()
        } else 0.0
    }

    fun getAverageSpeed(): Double {
        val validSessions = sessions.filter { it.averageSpeed > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.averageSpeed }.average()
        } else 0.0
    }

    fun getAverageScreenOnMilliAmperes(): Int {
        val validSessions = sessions.filter { it.screenOnMilliAmperes > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.screenOnMilliAmperes }.average().toInt()
        } else 0
    }

    fun getAverageScreenOffMilliAmperes(): Int {
        val validSessions = sessions.filter { it.screenOffMilliAmperes > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.screenOffMilliAmperes }.average().toInt()
        } else 0
    }

    fun getAverageMilliAmperes(): Int {
        val validSessions = sessions.filter { it.averageSpeedMilliAmperes > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.averageSpeedMilliAmperes }.average().toInt()
        } else 0
    }

    fun getTotalSessions(): Int = sessions.size

    /**
     * DISABLED: Sample session generation has been removed to ensure only real device data is used.
     * This method now does nothing to prevent any sample data generation.
     */
    fun addSampleSessionsIfEmpty() {
        android.util.Log.d("ChargingSessionManager", "Sample session generation DISABLED - only real charging sessions will be used")
        // No sample data generation - method intentionally left empty
    }
}