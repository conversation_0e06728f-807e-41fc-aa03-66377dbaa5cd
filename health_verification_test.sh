#!/bin/bash

# Health Fragment Data Flow Verification Script
# Bundle ID: com.fc.p.tj.charginganimation.batterycharging.chargeeffect

set -e

BUNDLE_ID="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
MAIN_ACTIVITY="com.tqhit.battery.one.activity.main.MainActivity"
APK_PATH="app/build/outputs/apk/debug/app-debug.apk"
LOG_DIR="health_verification_logs"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create log directory
mkdir -p "$LOG_DIR"

echo -e "${BLUE}=== Health Fragment Data Flow Verification ===${NC}"
echo "Bundle ID: $BUNDLE_ID"
echo "Timestamp: $TIMESTAMP"
echo "Log Directory: $LOG_DIR"
echo ""

# Function to log with timestamp
log_with_timestamp() {
    echo "[$(date '+%H:%M:%S')] $1"
}

# Detect and select device
DEVICE_ID=""
echo -e "${YELLOW}Detecting connected devices...${NC}"
DEVICES=$(adb devices | grep -v "List of devices" | grep "device$" | awk '{print $1}')
DEVICE_COUNT=$(echo "$DEVICES" | wc -l)

if [[ $DEVICE_COUNT -eq 0 ]]; then
    echo -e "${RED}No devices connected. Please connect a device or start an emulator.${NC}"
    exit 1
elif [[ $DEVICE_COUNT -eq 1 ]]; then
    DEVICE_ID=$(echo "$DEVICES" | head -1)
    echo -e "${GREEN}Using device: $DEVICE_ID${NC}"
else
    echo -e "${YELLOW}Multiple devices detected:${NC}"
    echo "$DEVICES" | nl
    echo -e "${BLUE}Selecting real device (non-emulator) for battery testing...${NC}"

    # Prefer real device over emulator for battery testing
    REAL_DEVICE=$(echo "$DEVICES" | grep -v "emulator" | head -1)
    if [[ -n "$REAL_DEVICE" ]]; then
        DEVICE_ID="$REAL_DEVICE"
        echo -e "${GREEN}Selected real device: $DEVICE_ID${NC}"
    else
        DEVICE_ID=$(echo "$DEVICES" | head -1)
        echo -e "${YELLOW}Using first available device: $DEVICE_ID${NC}"
    fi
fi

# Function to run ADB command with logging
run_adb_with_log() {
    local command="$1"
    local log_file="$2"
    local description="$3"

    # Replace 'adb' with 'adb -s $DEVICE_ID' in the command
    command=$(echo "$command" | sed "s/^adb /adb -s $DEVICE_ID /g")

    echo -e "${YELLOW}$description${NC}"
    log_with_timestamp "Executing: $command"

    if [[ "$log_file" != "" ]]; then
        eval "$command" > "$LOG_DIR/${log_file}_${TIMESTAMP}.log" 2>&1 &
        local pid=$!
        echo "Logging to: $LOG_DIR/${log_file}_${TIMESTAMP}.log (PID: $pid)"
        return $pid
    else
        eval "$command"
    fi
}

# Function to wait for user input
wait_for_user() {
    echo -e "${GREEN}Press Enter to continue to next phase...${NC}"
    read -r
}

echo -e "${BLUE}=== Phase 1: Deployment & Service Verification ===${NC}"

# Build the APK
echo -e "${YELLOW}Building APK...${NC}"
./gradlew assembleDebug

# Install the APK
echo -e "${YELLOW}Installing APK...${NC}"
adb -s "$DEVICE_ID" install -r "$APK_PATH"

# Start monitoring CoreBatteryStatsService
run_adb_with_log "adb logcat -s CoreBatteryStatsService CoreBatteryStatsProvider CORE_BATTERY_STATUS_CREATED" "phase1_service_startup" "Monitoring CoreBatteryStatsService startup..."
SERVICE_PID=$?

# Launch the app
echo -e "${YELLOW}Launching app...${NC}"
adb -s "$DEVICE_ID" shell am start -n "$BUNDLE_ID/$MAIN_ACTIVITY"

echo -e "${GREEN}✓ App launched. Check logcat for CoreBatteryStatsService startup.${NC}"
echo "Navigate to Health Fragment in the app UI."
wait_for_user

# Stop service monitoring
kill $SERVICE_PID 2>/dev/null || true

echo -e "${BLUE}=== Phase 2: Data Collection Monitoring ===${NC}"

# Monitor battery data collection
run_adb_with_log "adb logcat -s BatteryHistoryManager TemperatureHistoryManager BatteryRepository" "phase2_data_collection" "Monitoring battery data collection (1-minute intervals)..."
DATA_PID=$?

# Monitor health repository data flow
run_adb_with_log "adb logcat -s HealthRepository HEALTH_REPO DATA_FLOW" "phase2_health_repo" "Monitoring health repository data flow..."
HEALTH_PID=$?

# Monitor chart data generation
run_adb_with_log "adb logcat -s HealthChartData HEALTH_CHART_DATA_CREATED" "phase2_chart_data" "Monitoring chart data generation..."
CHART_PID=$?

echo -e "${GREEN}✓ Data collection monitoring started.${NC}"
echo "Wait for at least 2 minutes to observe data collection patterns."
echo "Look for minute-based battery/temperature entries in logs."
wait_for_user

# Stop data monitoring
kill $DATA_PID $HEALTH_PID $CHART_PID 2>/dev/null || true

echo -e "${BLUE}=== Phase 3: Real-Time Chart Updates ===${NC}"

# Monitor health fragment chart updates
run_adb_with_log "adb logcat -s HealthFragment CHART_DEBUG HealthViewModel" "phase3_chart_updates" "Monitoring health fragment chart updates..."
FRAGMENT_PID=$?

# Track data source (real vs sample)
run_adb_with_log "adb logcat | grep -E '(historical|sample|cached)'" "phase3_data_source" "Tracking data source (real vs sample)..."
SOURCE_PID=$?

echo -e "${GREEN}✓ Chart update monitoring started.${NC}"
echo "Switch between different time ranges (4h, 8h, 12h, 24h) in the Health Fragment."
echo "Observe chart updates and data source selection."
wait_for_user

# Stop chart monitoring
kill $FRAGMENT_PID $SOURCE_PID 2>/dev/null || true

echo -e "${BLUE}=== Phase 4: Battery State Change Testing ===${NC}"

# Monitor data updates during state changes
run_adb_with_log "adb logcat -s CoreBatteryStatsService HealthRepository | grep -E '(percentage|temperature|charging)'" "phase4_state_changes" "Monitoring data updates during battery state changes..."
STATE_PID=$?

echo -e "${YELLOW}Simulating battery state changes...${NC}"

# Simulate charging state changes
log_with_timestamp "Setting battery level to 50%"
adb -s "$DEVICE_ID" shell dumpsys battery set level 50

sleep 2

log_with_timestamp "Starting charging (AC)"
adb -s "$DEVICE_ID" shell dumpsys battery set ac 1

sleep 5

log_with_timestamp "Setting battery level to 75%"
adb -s "$DEVICE_ID" shell dumpsys battery set level 75

sleep 5

log_with_timestamp "Stopping charging"
adb -s "$DEVICE_ID" shell dumpsys battery set ac 0

sleep 2

log_with_timestamp "Resetting battery to normal"
adb -s "$DEVICE_ID" shell dumpsys battery reset

echo -e "${GREEN}✓ Battery state changes completed.${NC}"
echo "Check logs for real-time data updates during state changes."
wait_for_user

# Stop state monitoring
kill $STATE_PID 2>/dev/null || true

echo -e "${BLUE}=== Phase 5: Historical Data Verification ===${NC}"

# Monitor historical data loading
run_adb_with_log "adb logcat -s HistoryManager HealthRepository | grep -E '(loadHistory|getHistory)'" "phase5_historical_data" "Monitoring historical data loading..."
HISTORY_PID=$?

# Monitor chart data source selection
run_adb_with_log "adb logcat | grep -E 'Chart data updated.*source=(historical|sample)'" "phase5_chart_source" "Monitoring chart data source selection..."
CHART_SOURCE_PID=$?

echo -e "${YELLOW}Restarting app to test data persistence...${NC}"
adb -s "$DEVICE_ID" shell am force-stop "$BUNDLE_ID"
sleep 2
adb -s "$DEVICE_ID" shell am start -n "$BUNDLE_ID/$MAIN_ACTIVITY"

echo -e "${GREEN}✓ App restarted.${NC}"
echo "Navigate back to Health Fragment and observe data loading."
echo "Check if historical data persists and charts display correctly."
wait_for_user

# Stop historical monitoring
kill $HISTORY_PID $CHART_SOURCE_PID 2>/dev/null || true

echo -e "${BLUE}=== Phase 6: Edge Case Testing ===${NC}"

echo -e "${YELLOW}Testing fresh install scenario...${NC}"
echo -e "${RED}WARNING: This will clear all app data!${NC}"
echo -e "${GREEN}Press Enter to continue or Ctrl+C to skip...${NC}"
read -r

# Monitor sample data generation
run_adb_with_log "adb logcat -s HealthRepository | grep sample" "phase6_sample_data" "Monitoring sample data generation..."
SAMPLE_PID=$?

# Clear app data (simulate fresh install)
adb -s "$DEVICE_ID" shell pm clear "$BUNDLE_ID"
sleep 2
adb -s "$DEVICE_ID" shell am start -n "$BUNDLE_ID/$MAIN_ACTIVITY"

echo -e "${GREEN}✓ Fresh install simulation completed.${NC}"
echo "Navigate to Health Fragment and observe sample data generation."
echo "Charts should display sample data when no historical data exists."
wait_for_user

# Stop sample monitoring
kill $SAMPLE_PID 2>/dev/null || true

echo -e "${BLUE}=== Verification Complete ===${NC}"

# Generate summary report
SUMMARY_FILE="$LOG_DIR/verification_summary_${TIMESTAMP}.txt"
cat > "$SUMMARY_FILE" << EOF
Health Fragment Data Flow Verification Summary
==============================================
Timestamp: $TIMESTAMP
Bundle ID: $BUNDLE_ID

Test Phases Completed:
✓ Phase 1: Deployment & Service Verification
✓ Phase 2: Data Collection Monitoring  
✓ Phase 3: Real-Time Chart Updates
✓ Phase 4: Battery State Change Testing
✓ Phase 5: Historical Data Verification
✓ Phase 6: Edge Case Testing

Log Files Generated:
$(ls -la "$LOG_DIR"/*_${TIMESTAMP}.log 2>/dev/null | awk '{print "- " $9}' || echo "No log files found")

Next Steps:
1. Review log files for data collection patterns
2. Verify real vs sample data source selection
3. Confirm chart updates with battery state changes
4. Validate historical data persistence
5. Check sample data fallback functionality

Expected Success Criteria:
□ Real battery percentage changes reflected in charts within 60 seconds
□ Temperature variations captured and displayed accurately  
□ Historical data persists across app restarts
□ Charts show "historical" data source after 1+ hours of usage
□ Sample data fallback works for fresh installs
□ No chart clearing during time range switches
□ CoreBatteryStatsService integration confirmed via logcat
EOF

echo -e "${GREEN}✓ Verification summary saved to: $SUMMARY_FILE${NC}"
echo ""
echo -e "${BLUE}Review the generated log files to verify:${NC}"
echo "1. Real device data collection and storage"
echo "2. Chart data source selection (historical vs sample)"
echo "3. Real-time updates during battery state changes"
echo "4. Data persistence across app restarts"
echo "5. Sample data fallback for fresh installs"
echo ""
echo -e "${GREEN}Health Fragment verification completed successfully!${NC}"
